package com.wlink.agent.example;

import com.wlink.agent.client.model.music.FalQueueStatus;
import com.wlink.agent.client.model.music.Lyria2Input;
import com.wlink.agent.client.model.music.Lyria2Output;
import com.wlink.agent.service.MusicGenerationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 音乐生成示例
 * 演示如何使用 Fal AI Lyria2 音乐生成服务
 * 
 * 启用方式：在配置文件中设置 music.generation.example.enabled=true
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "music.generation.example.enabled", havingValue = "true")
public class MusicGenerationExample implements CommandLineRunner {

    private final MusicGenerationService musicGenerationService;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== Fal AI Lyria2 音乐生成示例 ===");
        
        try {
            // 示例1：简单的同步音乐生成
            example1_SimpleSyncGeneration();
            
            // 示例2：带种子值的音乐生成
            example2_GenerationWithSeed();
            
            // 示例3：异步音乐生成
            example3_AsyncGeneration();
            
            // 示例4：分步骤音乐生成（提交 -> 轮询 -> 获取结果）
            example4_StepByStepGeneration();
            
            // 示例5：完整参数音乐生成
            example5_FullParameterGeneration();
            
        } catch (Exception e) {
            log.error("音乐生成示例执行失败", e);
        }
        
        log.info("=== 音乐生成示例执行完成 ===");
    }

    /**
     * 示例1：简单的同步音乐生成
     */
    private void example1_SimpleSyncGeneration() {
        log.info("\n--- 示例1：简单的同步音乐生成 ---");
        
        try {
            String prompt = "A peaceful piano melody with gentle rain sounds in the background";
            
            log.info("开始生成音乐: {}", prompt);
            Lyria2Output result = musicGenerationService.generateMusicByPrompt(prompt);
            
            log.info("音乐生成完成!");
            log.info("音频URL: {}", result.getAudioUrl());
            log.info("文件大小: {}", result.getFormattedAudioFileSize());
            log.info("文件类型: {}", result.getAudioContentType());
            log.info("摘要: {}", result.getSummary());
            
        } catch (Exception e) {
            log.error("示例1执行失败", e);
        }
    }

    /**
     * 示例2：带种子值的音乐生成
     */
    private void example2_GenerationWithSeed() {
        log.info("\n--- 示例2：带种子值的音乐生成 ---");
        
        try {
            String prompt = "Upbeat electronic dance music with synthesizer beats";
            Integer seed = 12345;
            
            log.info("开始生成音乐（带种子值）: prompt={}, seed={}", prompt, seed);
            Lyria2Output result = musicGenerationService.generateMusicWithSeed(prompt, seed);
            
            log.info("音乐生成完成!");
            log.info("音频URL: {}", result.getAudioUrl());
            log.info("摘要: {}", result.getSummary());
            
        } catch (Exception e) {
            log.error("示例2执行失败", e);
        }
    }

    /**
     * 示例3：异步音乐生成
     */
    private void example3_AsyncGeneration() {
        log.info("\n--- 示例3：异步音乐生成 ---");
        
        try {
            String prompt = "Classical orchestral music with strings and woodwinds";
            
            log.info("开始异步生成音乐: {}", prompt);
            CompletableFuture<Lyria2Output> future = musicGenerationService.generateMusicByPromptAsync(prompt);
            
            log.info("异步任务已提交，等待完成...");
            
            // 可以在这里做其他事情
            log.info("可以在等待期间执行其他任务...");
            
            // 等待结果
            Lyria2Output result = future.get();
            
            log.info("异步音乐生成完成!");
            log.info("音频URL: {}", result.getAudioUrl());
            log.info("摘要: {}", result.getSummary());
            
        } catch (Exception e) {
            log.error("示例3执行失败", e);
        }
    }

    /**
     * 示例4：分步骤音乐生成（提交 -> 轮询 -> 获取结果）
     */
    private void example4_StepByStepGeneration() {
        log.info("\n--- 示例4：分步骤音乐生成 ---");
        
        try {
            String prompt = "Ambient soundscape with nature sounds and soft melodies";
            Lyria2Input input = Lyria2Input.simple(prompt);
            
            // 步骤1：提交请求
            log.info("步骤1：提交音乐生成请求");
            FalQueueStatus status = musicGenerationService.submitMusicGenerationRequest(input);
            String requestId = status.getRequestId();
            
            log.info("请求已提交: requestId={}, status={}", requestId, status.getStatusDescription());
            
            // 步骤2：手动轮询状态（可选）
            log.info("步骤2：查询任务状态");
            int pollCount = 0;
            while (!status.isCompleted() && pollCount < 5) {
                Thread.sleep(3000); // 等待3秒
                status = musicGenerationService.getTaskStatus(requestId);
                pollCount++;
                log.info("轮询 #{}: requestId={}, status={}", pollCount, requestId, status.getStatusDescription());
            }
            
            // 步骤3：等待完成并获取结果
            log.info("步骤3：等待任务完成");
            Lyria2Output result = musicGenerationService.waitForTaskCompletion(requestId);
            
            log.info("分步骤音乐生成完成!");
            log.info("音频URL: {}", result.getAudioUrl());
            log.info("摘要: {}", result.getSummary());
            
        } catch (Exception e) {
            log.error("示例4执行失败", e);
        }
    }

    /**
     * 示例5：完整参数音乐生成
     */
    private void example5_FullParameterGeneration() {
        log.info("\n--- 示例5：完整参数音乐生成 ---");
        
        try {
            String prompt = "Energetic rock music with electric guitar and drums";
            Integer seed = 54321;
            String negativePrompt = "slow tempo, quiet, ambient";
            
            log.info("开始生成音乐（完整参数）:");
            log.info("  提示词: {}", prompt);
            log.info("  种子值: {}", seed);
            log.info("  负面提示词: {}", negativePrompt);
            
            Lyria2Output result = musicGenerationService.generateMusicWithFullParams(prompt, seed, negativePrompt);
            
            log.info("音乐生成完成!");
            log.info("音频URL: {}", result.getAudioUrl());
            log.info("文件名: {}", result.getAudioFileName());
            log.info("文件大小: {}", result.getFormattedAudioFileSize());
            log.info("文件扩展名: {}", result.getAudioFileExtension());
            log.info("是否为WAV格式: {}", result.isAudioFormat("wav"));
            log.info("摘要: {}", result.getSummary());
            
        } catch (Exception e) {
            log.error("示例5执行失败", e);
        }
    }
}
