package com.wlink.agent.client.model.music;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Fal AI Lyria2 音乐生成请求参数
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Lyria2Input {

    /**
     * 描述要生成音乐的文本提示词
     * 必填字段，最少1个字符
     */
    @NotBlank(message = "提示词不能为空")
    @Size(min = 1, message = "提示词至少需要1个字符")
    private String prompt;

    /**
     * 用于确定性生成的种子值
     * 如果提供，模型将尝试在相同提示词和其他参数下生成相同的音频
     * 可选字段
     */
    private Integer seed;

    /**
     * 负面提示词，描述要从生成的音频中排除的内容
     * 默认值为 "low quality"
     */
    @JsonProperty("negative_prompt")
    @Builder.Default
    private String negativePrompt = "low quality";

    /**
     * 验证输入参数
     * 
     * @throws IllegalArgumentException 如果参数无效
     */
    public void validate() {
        if (prompt == null || prompt.trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
        
        if (prompt.length() > 2000) {
            throw new IllegalArgumentException("提示词长度不能超过2000个字符");
        }
        
        if (negativePrompt != null && negativePrompt.length() > 1000) {
            throw new IllegalArgumentException("负面提示词长度不能超过1000个字符");
        }
    }

    /**
     * 创建一个简单的音乐生成请求
     * 
     * @param prompt 音乐描述提示词
     * @return Lyria2Input实例
     */
    public static Lyria2Input simple(String prompt) {
        return Lyria2Input.builder()
                .prompt(prompt)
                .build();
    }

    /**
     * 创建一个带种子值的音乐生成请求
     * 
     * @param prompt 音乐描述提示词
     * @param seed 种子值
     * @return Lyria2Input实例
     */
    public static Lyria2Input withSeed(String prompt, Integer seed) {
        return Lyria2Input.builder()
                .prompt(prompt)
                .seed(seed)
                .build();
    }

    /**
     * 创建一个完整的音乐生成请求
     * 
     * @param prompt 音乐描述提示词
     * @param seed 种子值
     * @param negativePrompt 负面提示词
     * @return Lyria2Input实例
     */
    public static Lyria2Input full(String prompt, Integer seed, String negativePrompt) {
        return Lyria2Input.builder()
                .prompt(prompt)
                .seed(seed)
                .negativePrompt(negativePrompt)
                .build();
    }
}
