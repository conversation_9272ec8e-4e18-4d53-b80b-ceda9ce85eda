package com.wlink.agent.client.model.music;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Fal AI 文件信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FalFile {

    /**
     * 文件下载URL
     * 必填字段
     */
    private String url;

    /**
     * 文件MIME类型
     * 例如: "audio/wav", "audio/mp3"
     */
    @JsonProperty("content_type")
    private String contentType;

    /**
     * 文件名
     * 如果未提供将自动生成
     */
    @JsonProperty("file_name")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @JsonProperty("file_size")
    private Integer fileSize;

    /**
     * 文件数据（二进制格式）
     * 通常不在API响应中使用
     */
    @JsonProperty("file_data")
    private String fileData;

    /**
     * 检查文件是否为音频文件
     * 
     * @return 如果是音频文件返回true
     */
    public boolean isAudioFile() {
        return contentType != null && contentType.startsWith("audio/");
    }

    /**
     * 获取文件扩展名
     * 
     * @return 文件扩展名，如果无法确定则返回null
     */
    public String getFileExtension() {
        if (fileName != null && fileName.contains(".")) {
            return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        }
        
        if (contentType != null) {
            switch (contentType) {
                case "audio/wav":
                    return "wav";
                case "audio/mp3":
                case "audio/mpeg":
                    return "mp3";
                case "audio/ogg":
                    return "ogg";
                case "audio/flac":
                    return "flac";
                default:
                    return null;
            }
        }
        
        return null;
    }

    /**
     * 获取格式化的文件大小字符串
     * 
     * @return 格式化的文件大小，如 "1.2 MB"
     */
    public String getFormattedFileSize() {
        if (fileSize == null || fileSize <= 0) {
            return "未知大小";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 检查文件URL是否有效
     * 
     * @return 如果URL有效返回true
     */
    public boolean hasValidUrl() {
        return url != null && !url.trim().isEmpty() && 
               (url.startsWith("http://") || url.startsWith("https://"));
    }
}
