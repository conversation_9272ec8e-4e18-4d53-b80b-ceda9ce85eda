package com.wlink.agent.client.model.music;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Fal AI 队列状态信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FalQueueStatus {

    /**
     * 任务状态枚举
     */
    public enum Status {
        IN_QUEUE,      // 队列中
        IN_PROGRESS,   // 处理中
        COMPLETED      // 已完成
    }

    /**
     * 请求状态
     * 可能的值: IN_QUEUE, IN_PROGRESS, COMPLETED
     */
    private Status status;

    /**
     * 请求ID
     * 必填字段
     */
    @JsonProperty("request_id")
    private String requestId;

    /**
     * 响应URL
     * 用于获取最终结果
     */
    @JsonProperty("response_url")
    private String responseUrl;

    /**
     * 状态查询URL
     * 用于查询任务状态
     */
    @JsonProperty("status_url")
    private String statusUrl;

    /**
     * 取消任务URL
     * 用于取消正在进行的任务
     */
    @JsonProperty("cancel_url")
    private String cancelUrl;

    /**
     * 任务日志信息
     * 包含任务执行过程中的详细日志
     */
    private Map<String, Object> logs;

    /**
     * 任务指标信息
     * 包含性能指标和统计信息
     */
    private Map<String, Object> metrics;

    /**
     * 队列位置
     * 当状态为IN_QUEUE时，表示在队列中的位置
     */
    @JsonProperty("queue_position")
    private Integer queuePosition;

    /**
     * 检查任务是否已完成
     * 
     * @return 如果任务已完成返回true
     */
    public boolean isCompleted() {
        return Status.COMPLETED.equals(status);
    }

    /**
     * 检查任务是否正在处理中
     * 
     * @return 如果任务正在处理中返回true
     */
    public boolean isInProgress() {
        return Status.IN_PROGRESS.equals(status);
    }

    /**
     * 检查任务是否在队列中等待
     * 
     * @return 如果任务在队列中等待返回true
     */
    public boolean isInQueue() {
        return Status.IN_QUEUE.equals(status);
    }

    /**
     * 获取状态描述
     * 
     * @return 状态的中文描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知状态";
        }
        
        switch (status) {
            case IN_QUEUE:
                return queuePosition != null ? 
                    String.format("队列中 (位置: %d)", queuePosition) : "队列中";
            case IN_PROGRESS:
                return "处理中";
            case COMPLETED:
                return "已完成";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取预估等待时间描述
     * 基于队列位置估算等待时间
     * 
     * @return 预估等待时间描述
     */
    public String getEstimatedWaitTime() {
        if (!isInQueue() || queuePosition == null) {
            return "无法估算";
        }
        
        // 假设每个任务平均需要30秒处理时间
        int estimatedSeconds = queuePosition * 30;
        
        if (estimatedSeconds < 60) {
            return estimatedSeconds + "秒";
        } else if (estimatedSeconds < 3600) {
            return (estimatedSeconds / 60) + "分钟";
        } else {
            return String.format("%d小时%d分钟", 
                estimatedSeconds / 3600, 
                (estimatedSeconds % 3600) / 60);
        }
    }

    /**
     * 检查是否可以取消任务
     * 
     * @return 如果可以取消任务返回true
     */
    public boolean isCancellable() {
        return cancelUrl != null && !cancelUrl.trim().isEmpty() && 
               (isInQueue() || isInProgress());
    }
}
