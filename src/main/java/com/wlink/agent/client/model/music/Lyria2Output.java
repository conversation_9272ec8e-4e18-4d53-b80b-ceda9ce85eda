package com.wlink.agent.client.model.music;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Fal AI Lyria2 音乐生成响应结果
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Lyria2Output {

    /**
     * 生成的音乐文件
     * 包含音频文件的URL和相关信息
     */
    private FalFile audio;

    /**
     * 检查是否成功生成了音频
     * 
     * @return 如果成功生成音频返回true
     */
    public boolean hasAudio() {
        return audio != null && audio.hasValidUrl();
    }

    /**
     * 获取音频文件URL
     * 
     * @return 音频文件URL，如果不存在返回null
     */
    public String getAudioUrl() {
        return hasAudio() ? audio.getUrl() : null;
    }

    /**
     * 获取音频文件大小
     * 
     * @return 音频文件大小（字节），如果不存在返回null
     */
    public Integer getAudioFileSize() {
        return hasAudio() ? audio.getFileSize() : null;
    }

    /**
     * 获取音频文件类型
     * 
     * @return 音频文件MIME类型，如果不存在返回null
     */
    public String getAudioContentType() {
        return hasAudio() ? audio.getContentType() : null;
    }

    /**
     * 获取音频文件名
     * 
     * @return 音频文件名，如果不存在返回null
     */
    public String getAudioFileName() {
        return hasAudio() ? audio.getFileName() : null;
    }

    /**
     * 获取格式化的音频文件大小
     * 
     * @return 格式化的文件大小字符串，如 "1.2 MB"
     */
    public String getFormattedAudioFileSize() {
        return hasAudio() ? audio.getFormattedFileSize() : "未知大小";
    }

    /**
     * 获取音频文件扩展名
     * 
     * @return 音频文件扩展名，如果无法确定返回null
     */
    public String getAudioFileExtension() {
        return hasAudio() ? audio.getFileExtension() : null;
    }

    /**
     * 检查音频是否为指定格式
     * 
     * @param format 音频格式，如 "wav", "mp3"
     * @return 如果是指定格式返回true
     */
    public boolean isAudioFormat(String format) {
        if (!hasAudio() || format == null) {
            return false;
        }
        
        String extension = audio.getFileExtension();
        return format.equalsIgnoreCase(extension);
    }

    /**
     * 创建一个简单的输出结果
     * 
     * @param audioUrl 音频文件URL
     * @return Lyria2Output实例
     */
    public static Lyria2Output withAudioUrl(String audioUrl) {
        FalFile audioFile = FalFile.builder()
                .url(audioUrl)
                .build();
        
        return Lyria2Output.builder()
                .audio(audioFile)
                .build();
    }

    /**
     * 创建一个完整的输出结果
     * 
     * @param audioUrl 音频文件URL
     * @param contentType 文件MIME类型
     * @param fileName 文件名
     * @param fileSize 文件大小
     * @return Lyria2Output实例
     */
    public static Lyria2Output full(String audioUrl, String contentType, 
                                   String fileName, Integer fileSize) {
        FalFile audioFile = FalFile.builder()
                .url(audioUrl)
                .contentType(contentType)
                .fileName(fileName)
                .fileSize(fileSize)
                .build();
        
        return Lyria2Output.builder()
                .audio(audioFile)
                .build();
    }

    /**
     * 获取结果摘要信息
     * 
     * @return 结果摘要字符串
     */
    public String getSummary() {
        if (!hasAudio()) {
            return "未生成音频文件";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("音频文件已生成");
        
        if (audio.getFileName() != null) {
            summary.append(" - ").append(audio.getFileName());
        }
        
        if (audio.getFileSize() != null) {
            summary.append(" (").append(getFormattedAudioFileSize()).append(")");
        }
        
        return summary.toString();
    }
}
