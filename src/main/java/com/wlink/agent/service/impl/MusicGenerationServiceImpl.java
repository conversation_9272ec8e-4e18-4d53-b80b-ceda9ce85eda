package com.wlink.agent.service.impl;

import com.wlink.agent.client.FalAiLyria2Client;
import com.wlink.agent.client.model.music.FalQueueStatus;
import com.wlink.agent.client.model.music.Lyria2Input;
import com.wlink.agent.client.model.music.Lyria2Output;
import com.wlink.agent.service.MusicGenerationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;

/**
 * 音乐生成服务实现
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MusicGenerationServiceImpl implements MusicGenerationService {

    private final FalAiLyria2Client falAiLyria2Client;

    @Override
    public Lyria2Output generateMusic(Lyria2Input input) throws IOException {
        log.info("开始生成音乐: prompt={}", input.getPrompt());
        
        try {
            // 验证输入参数
            input.validate();
            
            // 同步生成音乐
            Lyria2Output result = falAiLyria2Client.generateMusicSync(input);
            
            log.info("音乐生成完成: prompt={}, audioUrl={}, summary={}", 
                    input.getPrompt(), result.getAudioUrl(), result.getSummary());
            
            return result;
            
        } catch (Exception e) {
            log.error("音乐生成失败: prompt={}, error={}", input.getPrompt(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Lyria2Output> generateMusicAsync(Lyria2Input input) {
        log.info("开始异步生成音乐: prompt={}", input.getPrompt());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return generateMusic(input);
            } catch (IOException e) {
                log.error("异步音乐生成失败: prompt={}, error={}", input.getPrompt(), e.getMessage(), e);
                throw new RuntimeException("异步音乐生成失败", e);
            }
        });
    }

    @Override
    public FalQueueStatus submitMusicGenerationRequest(Lyria2Input input) throws IOException {
        log.info("提交音乐生成请求: prompt={}", input.getPrompt());
        
        try {
            // 验证输入参数
            input.validate();
            
            // 提交请求
            FalQueueStatus status = falAiLyria2Client.submitMusicGenerationRequestWithRetry(input);
            
            log.info("音乐生成请求已提交: prompt={}, requestId={}, status={}", 
                    input.getPrompt(), status.getRequestId(), status.getStatus());
            
            return status;
            
        } catch (Exception e) {
            log.error("提交音乐生成请求失败: prompt={}, error={}", input.getPrompt(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public FalQueueStatus getTaskStatus(String requestId) throws IOException {
        log.debug("查询任务状态: requestId={}", requestId);
        
        try {
            FalQueueStatus status = falAiLyria2Client.getTaskStatus(requestId);
            
            log.debug("任务状态查询完成: requestId={}, status={}, description={}", 
                     requestId, status.getStatus(), status.getStatusDescription());
            
            return status;
            
        } catch (Exception e) {
            log.error("查询任务状态失败: requestId={}, error={}", requestId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Lyria2Output getTaskResult(String requestId) throws IOException {
        log.info("获取任务结果: requestId={}", requestId);
        
        try {
            Lyria2Output result = falAiLyria2Client.getTaskResultWithRetry(requestId);
            
            log.info("任务结果获取完成: requestId={}, audioUrl={}, summary={}", 
                    requestId, result.getAudioUrl(), result.getSummary());
            
            return result;
            
        } catch (Exception e) {
            log.error("获取任务结果失败: requestId={}, error={}", requestId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean cancelTask(String requestId) throws IOException {
        log.info("取消任务: requestId={}", requestId);
        
        try {
            boolean cancelled = falAiLyria2Client.cancelTask(requestId);
            
            if (cancelled) {
                log.info("任务取消成功: requestId={}", requestId);
            } else {
                log.warn("任务取消失败: requestId={}", requestId);
            }
            
            return cancelled;
            
        } catch (Exception e) {
            log.error("取消任务失败: requestId={}, error={}", requestId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Lyria2Output waitForTaskCompletion(String requestId) throws IOException {
        log.info("等待任务完成: requestId={}", requestId);
        
        try {
            Lyria2Output result = falAiLyria2Client.waitForCompletion(requestId);
            
            log.info("任务等待完成: requestId={}, audioUrl={}, summary={}", 
                    requestId, result.getAudioUrl(), result.getSummary());
            
            return result;
            
        } catch (Exception e) {
            log.error("等待任务完成失败: requestId={}, error={}", requestId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Lyria2Output generateMusicByPrompt(String prompt) throws IOException {
        if (prompt == null || prompt.trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
        
        Lyria2Input input = Lyria2Input.simple(prompt);
        return generateMusic(input);
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Lyria2Output> generateMusicByPromptAsync(String prompt) {
        log.info("开始异步生成音乐（简化方法）: prompt={}", prompt);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return generateMusicByPrompt(prompt);
            } catch (IOException e) {
                log.error("异步音乐生成失败（简化方法）: prompt={}, error={}", prompt, e.getMessage(), e);
                throw new RuntimeException("异步音乐生成失败", e);
            }
        });
    }

    @Override
    public Lyria2Output generateMusicWithSeed(String prompt, Integer seed) throws IOException {
        if (prompt == null || prompt.trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
        
        Lyria2Input input = Lyria2Input.withSeed(prompt, seed);
        return generateMusic(input);
    }

    @Override
    public Lyria2Output generateMusicWithFullParams(String prompt, Integer seed, String negativePrompt) throws IOException {
        if (prompt == null || prompt.trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
        
        Lyria2Input input = Lyria2Input.full(prompt, seed, negativePrompt);
        return generateMusic(input);
    }
}
