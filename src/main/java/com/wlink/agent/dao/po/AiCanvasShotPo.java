package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 画布分镜表
 * @TableName ai_canvas_shot
 */
@TableName(value ="ai_canvas_shot")
@Data
public class AiCanvasShotPo implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 画布ID
     */
    private Long canvasId;

    /**
     * 分镜编码，如C01-SC-02-2
     */
    private String code;

    /**
     * 原始分镜ID，转换时有值
     */
    private Long originalShotId;

    /**
     * 分镜类型
     */
    private String type;

    /**
     * 构图描述
     */
    private String composition;


    /**
     * 显示类型
     */
    private String displayType;

    /**
     * 运动类型
     */
    private String movement;

    /**
     * 排序序号
     */
    private Integer sortOrder;

    /**
     * 分镜状态(0-初始,1-处理中,2-已完成)
     */
    private Integer shotStatus;

    /**
     * 播放时长(毫秒)，默认5000毫秒
     */
    private Long playDuration;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}