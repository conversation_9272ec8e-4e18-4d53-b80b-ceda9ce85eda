spring:
  # ===================================================================
  # 核心应用信息 (必须在本地文件中)
  # ===================================================================
  application:
    name: smart-agent
  profiles:
    active: local # 激活 local 环境
  main:
    allow-bean-definition-overriding: true
  # ===================================================================
  # Nacos 连接引导配置 (关键部分！)
  # Spring Cloud Nacos 会优先读取这里的配置来初始化连接
  # ===================================================================
  cloud:
    nacos:
      # 全局 Nacos 服务地址 (同时用于服务发现和配置中心)
      server-addr: mse-0dd79260-p.nacos-ans.mse.aliyuncs.com:8848
      # --- 配置中心的专属配置 ---
      config:
        # server-addr 会从上面继承
        namespace: a614f3d8-bc67-44eb-82d7-62b7b6c0b668 # 全局默认命名空间
        group: local # 全局默认分组
        file-extension: yaml # 配置文件扩展名
      # --- 服务发现的专属配置 ---
      # server-addr, namespace, group 都会从上面继承
      # 如果服务发现的 group 或 namespace 不同，可以在这里覆盖
      # namespace: xxxxx
      # group: yyyyy

  # ===================================================================
  # 配置文件导入 (现在可以简化)
  # 它会自动使用上面 spring.cloud.nacos.config 中定义的 group 和 namespace
  # ===================================================================
  config:
    import:
      # 1. 导入应用主配置文件 (推荐格式)
      # 它会自动解析为: optional:nacos:smart-agent-local.yaml
      - "optional:nacos:${spring.application.name}.${spring.cloud.nacos.config.file-extension}"

      # 2. 导入共享配置文件
      # 因为上面定义了全局的 group 和 namespace，这里就不用再重复写了，更简洁！
      - "optional:nacos:wl-avatar-mysql.yaml"
      - "optional:nacos:wl-avatar-redis.yaml"
      - "optional:nacos:wl-avatar-rocketmq.yaml"
      - "optional:nacos:wl-xxl-job.yaml"

# ===================================================================
# 其他本地配置 (这些配置也可以放在 Nacos 中)
# 如果 Nacos 中有相同配置，Nacos 的优先级更高
# ===================================================================
logging:
  level:
    org.springframework.web.reactive.function.client.ExchangeFunctions: INFO
    org.springframework.beans.factory.support: INFO
    org.springframework.context.annotation: INFO
    org.springframework.boot.autoconfigure: INFO
    com.alibaba.cloud.nacos: INFO
    org.apache.rocketmq: DEBUG

# mybatis-plus配置控制台打印完整带参数SQL语句
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha          # 移到这里
    operations-sorter: alpha    # 移到这里
  packages-to-scan: com.wlink.agent
  paths-to-match: /**

# ... 其他本地配置，例如 mail, feign, http, wx 等可以保持不变 ...
# 它们最终会被 Nacos 中的配置覆盖（如果 Nacos 中存在的话）

# 微信支付配置
wx:
  pay:
    appId: wx70e251dfb6775641
    mchId: 1716346678
    mchKey: 35f1d04629ad483fafa09866558d809c
    apiV3Key: 35f1d04629ad483fafa09866558d809c
    notifyUrl: https://dev.neodomain.cn/agent/pay/callback/wx
    # 建议使用正斜杠 "/"，更具可移植性
    pubKeyPath: D:/wx/pub_key.pem
    payType: 0
    privateKeyPath: D:/wx/apiclient_key.pem  # 商户私钥路径
    privateCertPath: D:/wx/apiclient_cert.pem # 商户证书路径
    certSerialNo: 396FD0A30FD789BEE560B050FB69F234D97EB36E

# Fal AI 配置
fal-ai:
  base-url: https://queue.fal.run
  api-keys:
    lyria2: # 请在此处填入您的 Fal AI API Key